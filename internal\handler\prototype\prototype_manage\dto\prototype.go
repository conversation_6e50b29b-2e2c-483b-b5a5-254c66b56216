package dto

type CreatePrototypeResp struct {
	Message string `json:"message"`
}

type PrototypeListResp struct {
	Data          []map[string]interface{} `json:"data"`
	Size          int                      `json:"size"`
	IsEnd         bool                     `json:"isEnd"`
	ModelCategory []map[string]interface{} `json:"model_category"`
}

type DeletePrototypeResp struct {
	Message string `json:"message"`
}

type CheckPrototypeResp struct {
	Model   string `json:"model"`
	Barcode string `json:"barcode"`
}

// PrototypeLimitResponse 代理商样机限制响应
type PrototypeLimitResponse struct {
	PrototypeLimit          int   `json:"prototype_limit"`           // 样机数量限制
	PrototypeFrequencyLimit int   `json:"prototype_frequency_limit"` // 样机频次限制
	PrototypeTotal          int64 `json:"prototype_total"`           // 当前总样机数
	PrototypeMonthTotal     int64 `json:"prototype_month_total"`     // 当前月样机数
}

// PrototypeUserListResponse 代理商演示用户列表响应
type PrototypeUserListResponse struct {
	UserList  []PrototypeUserInfo `json:"user_list"`  // 用户列表
	UserLimit int                 `json:"user_limit"` // 用户限制（-1表示无限制）
}

// PrototypeUserInfo 演示用户信息
type PrototypeUserInfo struct {
	ID    int    `json:"id"`    // 用户ID
	Phone string `json:"phone"` // 手机号
}

package service

import (
	"encoding/json"
	"marketing-app/internal/handler/dto"
	"marketing-app/internal/pkg/log"
	"marketing-app/internal/pkg/utils"
	"marketing-app/internal/repository"

	"github.com/gin-gonic/gin"
)

type NotificationService interface {
	// GetUserMessages 获取用户通知消息
	GetUserMessages(c *gin.Context, userID uint, req *dto.NotificationMessagesReq) ([]dto.NotificationMessage, error)
}

type notificationService struct {
	notificationRepo repository.NotificationRepository
}

func NewNotificationService(notificationRepo repository.NotificationRepository) NotificationService {
	return &notificationService{
		notificationRepo: notificationRepo,
	}
}

func (s *notificationService) GetUserMessages(c *gin.Context, userID uint, req *dto.NotificationMessagesReq) ([]dto.NotificationMessage, error) {
	// 设置默认页面大小
	pageSize := req.PageSize
	if pageSize == 0 {
		pageSize = 100
	}

	// 从数据库获取通知消息
	messages, err := s.notificationRepo.GetUserNotifications(c, userID, req.Platform, pageSize, req.Page)
	if err != nil {
		return nil, err
	}

	// 转换为响应格式
	var result []dto.NotificationMessage
	var ids []uint

	for _, msg := range messages {
		// 解析content JSON
		var content map[string]interface{}
		if err := json.Unmarshal(msg.Content, &content); err != nil {
			// 如果解析失败，跳过这条消息
			continue
		}

		// 添加额外字段到content中
		content["id"] = msg.ID
		content["created_at"] = msg.CreatedAt
		content["slug"] = msg.Slug
		content["read"] = msg.Read
		content["checked"] = msg.Checked
		content["group_icon"] = utils.AddPrefix(content["group_icon"].(string))
		notificationMsg := dto.NotificationMessage{
			ID:        msg.ID,
			CreatedAt: msg.CreatedAt,
			Slug:      msg.Slug,
			Read:      msg.Read,
			Checked:   msg.Checked,
			Content:   content,
		}

		result = append(result, notificationMsg)
		ids = append(ids, msg.ID)
	}

	// 如果有消息，更新拉取状态
	if len(ids) > 0 {
		err = s.notificationRepo.UpdateInboxFetched(c, ids)
		if err != nil {
			// 记录错误但不影响返回结果
			// 可以在这里添加日志记录
			log.Error("更新消息拉取记录失败")
		}
	}

	return result, nil
}

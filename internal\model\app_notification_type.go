package model

import (
	"time"
)

// AppNotificationType 消息类型表结构体
type AppNotificationType struct {
	ID         uint      `gorm:"column:id;type:int(10) unsigned;primary_key;AUTO_INCREMENT" json:"id"`
	Name       string    `gorm:"column:name;type:varchar(20);not null" json:"name"`             // 消息类型名称
	Icon       string    `gorm:"column:icon;type:varchar(100);not null" json:"icon"`            // 消息图标
	Slug       string    `gorm:"column:slug;type:varchar(30);not null;uniqueIndex" json:"slug"` // 唯一标识
	CreatedAt  time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt  time.Time `gorm:"column:updated_at;type:timestamp;not null" json:"updated_at"`
	Manual     uint8     `gorm:"column:manual;type:tinyint(3) unsigned;not null;default:1" json:"manual"`              // 是否可手动推送(1-是，0-否)
	Action     string    `gorm:"column:action;type:enum('none','forward','check_app_upgrade');not null" json:"action"` // 点击消息后的动作
	Url        string    `gorm:"column:url;type:varchar(100);not null;default:''" json:"url"`                          // 跳转的路径
	MediaUrl   string    `gorm:"column:media_url;type:varchar(100);not null;default:''" json:"media_url"`              // 非文本消息的素材地址
	MsgType    string    `gorm:"column:msg_type;type:enum('text','image');not null;default:'text'" json:"msg_type"`    // 消息类型
	ActionText string    `gorm:"column:action_text;type:varchar(10);not null;default:''" json:"action_text"`           // 动作按钮提示文字
	Popup      uint8     `gorm:"column:popup;type:tinyint(3) unsigned;not null;default:0" json:"popup"`                // 是否需要弹窗(1-是，0-否)
	Banner     uint8     `gorm:"column:banner;type:tinyint(3) unsigned;not null;default:0" json:"banner"`              // 是否在工作台显示横幅广告(1-是，0-否)
}

// TableName 指定表名
func (a *AppNotificationType) TableName() string {
	return "app_notification_type"
}

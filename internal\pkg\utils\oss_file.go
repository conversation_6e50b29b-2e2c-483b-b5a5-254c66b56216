package utils

import "strings"

const (
	OldPrefix = "https://dt1.readboy.com/"
	NewPrefix = "https://static.readboy.com/"
)

// AddPrefix 根据url内容添加相应前缀
func AddPrefix(url string) string {
	if url == "" {
		return url
	}
	if strings.HasPrefix(url, "https://") || strings.HasPrefix(url, "http://") {
		return url
	}
	if strings.HasPrefix(url, "rbcare") {
		return OldPrefix + url
	}
	return NewPrefix + url
}

// DeletePrefix 删除url中的前缀
func DeletePrefix(url string) string {
	if url == "" {
		return url
	}
	url = strings.ReplaceAll(url, OldPrefix, "")
	return strings.ReplaceAll(url, NewPrefix, "")
}

package notification

import (
	handler "marketing-app/internal/handler"
	"marketing-app/internal/pkg/db"
	"marketing-app/internal/repository"
	"marketing-app/internal/service"

	"github.com/gin-gonic/gin"
)

type NoticeRouter struct {
	notification handler.NotificationHandler
}

func NewNoticeRouter() *NoticeRouter {
	database, _ := db.GetDB()
	repo := repository.NewNotificationRepository(database)
	svc := service.NewNotificationService(repo)
	h := handler.NewNotificationHandler(svc)
	return &NoticeRouter{
		notification: h,
	}
}

func (p *NoticeRouter) Register(r *gin.RouterGroup) {
	g := r.Group("/notification")
	{
		g.GET("", p.notification.Notification)
		g.POST("/read", p.notification.Read)
		g.POST("/checked", p.notification.Checked)
	}
}

package user

import (
	"marketing-app/internal/model"
	"marketing-app/internal/repository/user/dto"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type User interface {
	GetAgencyInfo(c *gin.Context, uid int) (*dto.AgencyInfo, error)
}

type user struct {
	db *gorm.DB
}

func NewUser(db *gorm.DB) User {
	return &user{db: db}
}

// GetAgencyInfo 获取代理商信息
func (u *user) GetAgencyInfo(c *gin.Context, uid int) (*dto.AgencyInfo, error) {
	var agencyInfo model.UserAgency
	err := u.db.WithContext(c).
		Where("uid = ? AND status = 1", uid).
		First(&agencyInfo).Error
	if err != nil {
		return nil, err
	}

	// 转换为DTO
	result := &dto.AgencyInfo{
		TopAgency:    agencyInfo.TopAgency,
		SecondAgency: agencyInfo.SecondAgency,
	}

	return result, nil
}

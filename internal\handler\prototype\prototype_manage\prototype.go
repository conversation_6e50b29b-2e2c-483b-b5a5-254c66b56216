package prototype_manage

import (
	"marketing-app/internal/handler"
	"marketing-app/internal/handler/prototype/prototype_manage/dto"
	"marketing-app/internal/pkg/convertor/prototype_convertor"
	"marketing-app/internal/router/prototype/prototype_manage/client"
	service "marketing-app/internal/service/prototype/prototype_manage"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
)

type Prototype interface {
	CreatePrototype(c *gin.Context)
	ListPrototype(c *gin.Context)
	DeletePrototype(c *gin.Context)
	CheckPrototype(c *gin.Context)
	GetPrototypeLimit(c *gin.Context)
	GetPrototypeUserList(c *gin.Context)
}

type prototype struct {
	prototypeSvc service.PrototypeSvc
}

func NewPrototypeHandler(svc service.PrototypeSvc) Prototype {
	return &prototype{prototypeSvc: svc}
}

func (p *prototype) CreatePrototype(c *gin.Context) {
	var (
		req  client.CreatePrototypeRequest
		err  error
		resp dto.CreatePrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	// 测试环境下通过json直接传uid，发布模式下通过传token并且经过auth后从context中获取
	if gin.Mode() == gin.ReleaseMode {
		req.Uid = c.MustGet("uid").(int)
	}
	err = p.prototypeSvc.CreatePrototype(
		c,
		prototype_convertor.NewPrototypeCreateConvertor().ClientToEntity(&req),
	)
	resp = dto.CreatePrototypeResp{
		Message: "添加成功！",
	}
}

func (p *prototype) ListPrototype(c *gin.Context) {
	var (
		req  client.ListPrototypeRequest
		err  error
		resp *dto.PrototypeListResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	modelCategory, data, err := p.prototypeSvc.GetPrototypeListData(
		c,
		prototype_convertor.NewPrototypeListConvertor().ClientToEntity(&req),
	)
	var categoryList []map[string]interface{}
	for _, category := range modelCategory {
		categoryList = append(categoryList, map[string]interface{}{
			"id":   category.ID,
			"name": category.Name,
		})
	}
	resp = &dto.PrototypeListResp{
		Data:          nil,
		Size:          0,
		IsEnd:         false,
		ModelCategory: categoryList,
	}
	if data != nil {
		resp.Data = data.Data
		resp.Size = data.Size
		resp.IsEnd = data.IsEnd
	}
	if resp.Data == nil {
		err = errors.New("未找到数据")
		return
	}
}

func (p *prototype) DeletePrototype(c *gin.Context) {
	var (
		req  client.DeletePrototypeRequest
		err  error
		resp *dto.DeletePrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()
	if err = c.ShouldBindJSON(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}
	if req.Barcode == "" || req.EndpointID == 0 {
		err = errors.New("参数错误")
		return
	}
	msg, err := p.prototypeSvc.DeletePrototype(
		c,
		prototype_convertor.NewPrototypeDeleteConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}
	resp = &dto.DeletePrototypeResp{
		Message: msg,
	}
}

func (p *prototype) CheckPrototype(c *gin.Context) {
	var (
		req  client.CheckPrototypeRequest
		err  error
		resp *dto.CheckPrototypeResp
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	if err = c.ShouldBindQuery(&req); err != nil {
		err = errors.Wrap(err, "bind request error")
		return
	}

	if req.Barcode == "" {
		err = errors.New("参数错误")
		return
	}

	model, barcode, err := p.prototypeSvc.CheckPrototype(
		c,
		prototype_convertor.NewPrototypeCheckConvertor().ClientToEntity(&req),
	)
	if err != nil {
		return
	}

	resp = &dto.CheckPrototypeResp{
		Model:   model,
		Barcode: barcode,
	}
}

// GetPrototypeLimit 获取代理商样机限制信息
func (p *prototype) GetPrototypeLimit(c *gin.Context) {
	var (
		err  error
		resp *dto.PrototypeLimitResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 从认证token中获取用户ID
	var uid int
	if gin.Mode() == gin.ReleaseMode {
		uid = c.MustGet("uid").(int)
	} else {
		// 测试环境下可以从请求参数获取
		uidStr := c.Query("uid")
		if uidStr == "" {
			err = errors.New("用户ID不能为空")
			return
		}
		uid, err = strconv.Atoi(uidStr)
		if err != nil {
			err = errors.Wrap(err, "用户ID格式错误")
			return
		}
	}

	// 调用服务层获取样机限制信息
	r, err := p.prototypeSvc.GetPrototypeLimit(c, uid)
	if err != nil {
		return
	}
	resp = &dto.PrototypeLimitResponse{
		PrototypeLimit:          r.PrototypeLimit,
		PrototypeFrequencyLimit: r.PrototypeFrequencyLimit,
		PrototypeTotal:          r.PrototypeTotal,
		PrototypeMonthTotal:     r.PrototypeMonthTotal,
	}
}

// GetPrototypeUserList 获取代理商演示用户列表
func (p *prototype) GetPrototypeUserList(c *gin.Context) {
	var (
		err  error
		resp *dto.PrototypeUserListResponse
	)
	defer func() {
		if err != nil {
			handler.ResponseError(c, err)
		} else {
			handler.ResponseSuccess(c, resp)
		}
	}()

	// 从认证token中获取用户ID
	var uid int
	if gin.Mode() == gin.ReleaseMode {
		uid = c.MustGet("uid").(int)
	} else {
		// 测试环境下可以从请求参数获取
		uidStr := c.Query("uid")
		if uidStr == "" {
			err = errors.New("用户ID不能为空")
			return
		}
		uid, err = strconv.Atoi(uidStr)
		if err != nil {
			err = errors.Wrap(err, "用户ID格式错误")
			return
		}
	}

	// 调用服务层获取演示用户列表
	r, err := p.prototypeSvc.GetPrototypeUserList(c, uid)
	if err != nil {
		return
	}
	// 转换用户列表数据
	var userList []dto.PrototypeUserInfo
	for _, user := range r.UserList {
		userList = append(userList, dto.PrototypeUserInfo{
			ID:    user.ID,
			Phone: user.Phone,
		})
	}

	resp = &dto.PrototypeUserListResponse{
		UserList:  userList,
		UserLimit: r.UserLimit,
	}
}

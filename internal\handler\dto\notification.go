package dto

import "time"

// NotificationMessagesReq 获取通知消息请求
type NotificationMessagesReq struct {
	Platform string `form:"platform"  json:"platform"`
	PageSize int    `form:"page_size" binding:"min=0,max=500" json:"page_size"`
	Page     int    `form:"page" binding:"min=0" json:"page"`
}

// NotificationMessage 通知消息响应
type NotificationMessage struct {
	ID        uint                   `json:"id"`
	CreatedAt time.Time              `json:"created_at"`
	Slug      string                 `json:"slug"`
	Read      int8                   `json:"read"`
	Checked   int8                   `json:"checked"`
	Content   map[string]interface{} `json:",inline"` // 内容字段会被展开到根级别
}

// NotificationMessagesResp 获取通知消息响应
type NotificationMessagesResp struct {
	Messages []NotificationMessage `json:"messages"`
}

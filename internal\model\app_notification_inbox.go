package model

import "time"

// AppNotificationInbox 用户通知收件箱表
type AppNotificationInbox struct {
	ID             uint64    `gorm:"primaryKey;column:id;type:bigint(20) unsigned;not null" json:"id"`
	UserID         uint      `gorm:"column:user_id;type:int(10) unsigned;not null;index;comment:用户ID" json:"user_id"`
	NotificationID uint      `gorm:"column:notification_id;type:int(10) unsigned;not null;index:notification_id;comment:通知ID" json:"notification_id"`
	Fetched        uint8     `gorm:"column:fetched;type:tinyint(3) unsigned;not null;default:0;comment:是否已经拉取" json:"fetched"`
	Read           uint8     `gorm:"column:read;type:tinyint(3) unsigned;not null;default:0;comment:消息是否已被浏览" json:"read"`
	Checked        uint8     `gorm:"column:checked;type:tinyint(3) unsigned;not null;default:0;comment:消息是否已被点击查看" json:"checked"`
	CreatedAt      time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
}

// TableName 设置表名
func (AppNotificationInbox) TableName() string {
	return "app_notification_inbox"
}

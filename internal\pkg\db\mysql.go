package db

import (
	"errors"
	"fmt"
	"marketing-app/internal/pkg/config"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 全局实例和同步锁
var (
	manager *Manager
	once    sync.Once
	mu      sync.RWMutex
)

// Config 单个MySQL数据库配置
type Config struct {
	DSN          string        `json:"dsn" yaml:"dsn"`
	MaxOpenConns int           `json:"max_open_conns" yaml:"max_open_conns"`
	MaxIdleConns int           `json:"max_idle_conns" yaml:"max_idle_conns"`
	MaxLifetime  time.Duration `json:"max_lifetime" yaml:"max_lifetime"`
	Level        int           `json:"level" yaml:"level"` // 0=silent,1=error,2=warn,3=info
}

// Manager 数据库管理器
type Manager struct {
	dbs map[string]*gorm.DB
}

// Init 初始化数据库管理器 (线程安全)
func Init() error {
	var initErr error
	var configs map[string]*Config
	if err := config.UnmarshalKey("databases", &configs); err != nil {
		panic(fmt.Sprintf("解析数据库配置失败: %v", err))
	}
	if gin.Mode() != gin.DebugMode {
		for name, cfg := range configs {
			cfg.DSN = config.GetString("databases." + name + ".dsn")
			if cfg.DSN == "" {
				panic(fmt.Sprintf("数据库 %s 的 DSN 未配置", name))
			}
		}
	}

	once.Do(func() {
		mu.Lock()
		defer mu.Unlock()

		manager = &Manager{
			dbs: make(map[string]*gorm.DB),
		}

		for name, cfg := range configs {
			if err := manager.addDB(name, cfg); err != nil {
				initErr = fmt.Errorf("初始化数据库 %s 失败: %w", name, err)
				return
			}
		}
	})

	return initErr
}

// addDB 添加单个数据库连接
func (m *Manager) addDB(name string, cfg *Config) error {
	// 解析GORM日志级别
	logLevel := logger.Silent
	switch cfg.Level {
	case 1:
		logLevel = logger.Error
	case 2:
		logLevel = logger.Warn
	case 3:
		logLevel = logger.Info
	}

	// 创建GORM配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
	}

	// 打开数据库连接
	db, err := gorm.Open(mysql.Open(cfg.DSN), gormConfig)
	if err != nil {
		return fmt.Errorf("连接数据库 %s 失败: %w", name, err)
	}

	// 获取底层sql.DB并配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("获取数据库 %s 底层连接失败: %w", name, err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.MaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("测试数据库 %s 连接失败: %w", name, err)
	}

	m.dbs[name] = db
	return nil
}

// GetDB 获取指定名称的数据库连接
func GetDB(name ...string) (*gorm.DB, error) {
	mu.RLock()
	defer mu.RUnlock()

	if manager == nil {
		return nil, errors.New("数据库管理器未初始化")
	}

	key := "default"
	if len(name) > 0 && name[0] != "" {
		key = name[0]
	}

	db, ok := manager.dbs[key]
	if !ok {
		return nil, fmt.Errorf("数据库 %s 未配置", key)
	}
	return db, nil
}

// MustGetDB 获取指定名称的数据库连接，不存在则panic
func MustGetDB(name ...string) *gorm.DB {
	db, err := GetDB(name...)
	if err != nil {
		panic(err)
	}
	return db
}

// CloseAll 关闭所有数据库连接
func CloseAll() error {
	mu.Lock()
	defer mu.Unlock()

	if manager == nil {
		return nil
	}

	var err error
	for name, db := range manager.dbs {
		sqlDB, dbErr := db.DB()
		if dbErr != nil {
			err = errors.Join(err, fmt.Errorf("获取数据库 %s 底层连接失败: %w", name, dbErr))
			continue
		}

		if closeErr := sqlDB.Close(); closeErr != nil {
			err = errors.Join(err, fmt.Errorf("关闭数据库 %s 失败: %w", name, closeErr))
		}
	}

	manager = nil
	return err
}
